import discord
import logging
from typing import Optional, Callable, Any
from functools import wraps
from database import DatabaseManager

logger = logging.getLogger(__name__)

def require_license(db: DatabaseManager):
    """Decorator to require a valid license for command execution"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(interaction: discord.Interaction, *args, **kwargs):
            if not interaction.guild:
                await interaction.response.send_message("❌ This command can only be used in a server!", ephemeral=True)
                return
            
            server_id = interaction.guild.id
            
            # Check if server has a valid license
            if not db.is_server_licensed(server_id):
                embed = discord.Embed(
                    title="❌ License Required",
                    description="This server requires a valid license key to use bot commands.",
                    color=discord.Color.red()
                )
                embed.add_field(
                    name="How to get started:",
                    value="Use `/redeem-key <your-license-key>` to activate this server.",
                    inline=False
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Execute the original function
            return await func(interaction, *args, **kwargs)
        return wrapper
    return decorator

def require_configuration(db: DatabaseManager, fields: list = None):
    """Decorator to require server configuration for command execution"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(interaction: discord.Interaction, *args, **kwargs):
            if not interaction.guild:
                await interaction.response.send_message("❌ This command can only be used in a server!", ephemeral=True)
                return
            
            server_id = interaction.guild.id
            
            # Check if server is configured
            is_configured, missing_fields = db.is_server_configured(server_id)
            
            if not is_configured:
                embed = discord.Embed(
                    title="⚙️ Configuration Required",
                    description="This server needs to be configured before using this command.",
                    color=discord.Color.orange()
                )
                
                config_commands = {
                    'role_id': '`/rep <trigger-word> <role> <channel>`',
                    'trigger_word': '`/rep <trigger-word> <role> <channel>`',
                    'channel_id': '`/rep <trigger-word> <role> <channel>`',
                    'log_channel_id': '`/set-log-id <log-channel-id>`'
                }
                
                missing_configs = [config_commands.get(field, field) for field in missing_fields]
                embed.add_field(
                    name="Missing Configuration:",
                    value="\n".join(missing_configs),
                    inline=False
                )
                
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Execute the original function
            return await func(interaction, *args, **kwargs)
        return wrapper
    return decorator

def require_key_owner(db: DatabaseManager):
    """Decorator to require the user to own the license key being operated on"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(interaction: discord.Interaction, license_key: str = None, *args, **kwargs):
            if not license_key:
                await interaction.response.send_message("❌ License key is required!", ephemeral=True)
                return
                
            user_id = interaction.user.id
            user_keys = db.get_user_license_keys(user_id)
            
            # Check if user owns this key
            key_owned = any(key_doc['key'] == license_key for key_doc in user_keys)
            
            if not key_owned:
                await interaction.response.send_message("❌ You don't own this license key!", ephemeral=True)
                return
            
            # Execute the original function
            return await func(interaction, license_key, *args, **kwargs)
        return wrapper
    return decorator

def is_server_owner(user: discord.Member, guild: discord.Guild) -> bool:
    """Check if user is the server owner"""
    return user.id == guild.owner_id

def format_license_embed(title: str, description: str, color: discord.Color) -> discord.Embed:
    """Create a formatted embed for license-related messages"""
    embed = discord.Embed(
        title=title,
        description=description,
        color=color
    )
    embed.set_footer(text="Leakin Bot - Multi-Server License System")
    return embed

def validate_discord_id(id_str: str) -> Optional[int]:
    """Validate and convert Discord ID string to integer"""
    try:
        discord_id = int(id_str)
        # Discord snowflake IDs are typically 17-19 digits
        if len(str(discord_id)) >= 17:
            return discord_id
        return None
    except ValueError:
        return None

def parse_license_keys_from_text(text: str) -> list[str]:
    """Parse license keys from uploaded text file content"""
    # Split by whitespace and filter out empty strings
    keys = [key.strip() for key in text.split() if key.strip()]
    # Remove duplicates while preserving order
    seen = set()
    unique_keys = []
    for key in keys:
        if key not in seen:
            seen.add(key)
            unique_keys.append(key)
    return unique_keys

async def get_guild_member_safe(guild: discord.Guild, user_id: int) -> Optional[discord.Member]:
    """Safely get a guild member by ID"""
    try:
        member = guild.get_member(user_id)
        if not member:
            member = await guild.fetch_member(user_id)
        return member
    except discord.NotFound:
        return None
    except discord.HTTPException:
        return None

async def get_channel_safe(guild: discord.Guild, channel_id: int) -> Optional[discord.TextChannel]:
    """Safely get a text channel by ID"""
    try:
        channel = guild.get_channel(channel_id)
        if not channel:
            channel = await guild.fetch_channel(channel_id)
        if isinstance(channel, discord.TextChannel):
            return channel
        return None
    except discord.NotFound:
        return None
    except discord.HTTPException:
        return None

async def get_role_safe(guild: discord.Guild, role_id: int) -> Optional[discord.Role]:
    """Safely get a role by ID"""
    try:
        role = guild.get_role(role_id)
        if not role:
            # Roles can't be fetched, only gotten from cache
            return None
        return role
    except Exception:
        return None
