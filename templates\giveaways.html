﻿{% extends "base.html" %}

{% block title %}Giveaways - {{ server_info.name }}{% endblock %}

{% block content %}
<style>
    .giveaway-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        background: #2f3136;
        margin-bottom: 1rem;
    }
    .giveaway-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    .giveaway-header {
        background: #36393f;
        padding: 1rem;
        border-bottom: 1px solid #40444b;
    }
    .giveaway-body {
        padding: 1.25rem;
    }
    .giveaway-prize {
        font-size: 1.25rem;
        font-weight: 600;
        color: #fff;
        margin-bottom: 0.5rem;
        display: block;
        text-decoration: none;
    }
    .giveaway-prize:hover {
        color: #7289da;
        text-decoration: none;
    }
    .giveaway-meta {
        display: flex;
        gap: 1.5rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #b9bbbe;
        font-size: 0.9rem;
    }
    .meta-item i {
        color: #7289da;
        width: 16px;
        text-align: center;
    }
    .status-badge {
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 600;
        border-radius: 4px;
    }
    .status-active {
        background-color: rgba(67, 181, 129, 0.1);
        color: #43b581;
    }
    .status-ended {
        background-color: rgba(240, 71, 71, 0.1);
        color: #f04747;
    }
    .status-expired {
        background-color: rgba(250, 166, 26, 0.1);
        color: #faa61a;
    }
    .create-giveaway-btn {
        background-color: #5865f2;
        border: none;
        padding: 0.5rem 1.25rem;
        font-weight: 500;
        transition: background-color 0.2s;
    }
    .create-giveaway-btn:hover {
        background-color: #4752c4;
    }
    .server-card {
        background: #2f3136;
        border: none;
        border-radius: 8px;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-white"><i class="fas fa-gift me-2"></i>Giveaways</h2>
                <button class="btn create-giveaway-btn" data-bs-toggle="modal" data-bs-target="#createGiveawayModal" 
                        {% if active_giveaways_count >= 5 %}disabled title="Maximum of 5 active giveaways reached"{% endif %}>
                    <i class="fas fa-plus me-2"></i>Create Giveaway
                </button>
            </div>

            <!-- Server Info -->
            <div class="card server-card mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        {% if server_info.icon %}
                        <img src="{{ server_info.icon }}" alt="Server Icon" class="rounded-circle me-3" width="50" height="50">
                        {% else %}
                        <div class="bg-secondary rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-server text-white"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h5 class="mb-1 text-white">{{ server_info.name }}</h5>
                            <div class="d-flex gap-3">
                                <small class="text-muted"><i class="fas fa-users me-1"></i> {{ server_info.member_count }} members</small>
                                <small class="text-muted"><i class="fas fa-gift me-1"></i> {{ active_giveaways_count }} active giveaways</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Giveaways List -->
            <div class="card server-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-white"><i class="fas fa-list me-2"></i>Recent Giveaways</h5>
                    <small class="text-muted">Showing latest {{ giveaways|length }} giveaways</small>
                </div>
                <div class="card-body">
                    {% if giveaways %}
                        <div class="row">
                            {% for giveaway in giveaways %}
                            <div class="col-md-6 mb-3">
                                <div class="giveaway-card">
                                    <div class="giveaway-header">
                                        <a href="#" class="giveaway-prize" data-bs-toggle="modal" data-bs-target="#giveawayModal{{ loop.index }}">
                                            {{ giveaway.item }}
                                        </a>
                                        <div class="d-flex align-items-center gap-2">
                                            {% set is_active = not giveaway.ended and (not giveaway.end_time or giveaway.end_time > now_utc) %}
                                            <span class="status-badge {% if giveaway.ended %}status-ended{% elif not is_active %}status-expired{% else %}status-active{% endif %}">
                                                {% if giveaway.ended %}
                                                    ENDED
                                                {% elif not is_active %}
                                                    EXPIRED
                                                {% else %}
                                                    ACTIVE
                                                {% endif %}
                                            </span>
                                            <span class="text-muted small">#{{ giveaway.channel_id }}</span>
                                        </div>
                                    </div>
                                    <div class="giveaway-body">
                                        <p class="text-muted mb-3">
                                            {{ giveaway.requirements[:200] }}{% if giveaway.requirements|length > 200 %}...{% endif %}
                                        </p>
                                        <div class="giveaway-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-trophy"></i>
                                                <span>{{ giveaway.winners }} winner{% if giveaway.winners > 1 %}s{% endif %}</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-users"></i>
                                                <span>{{ giveaway.entries|length }} entries</span>
                                            </div>
                                            <div class="meta-item">
                                                <i class="fas fa-clock"></i>
                                                <span>{% if giveaway.end_time %}{{ giveaway.end_time.strftime('%b %d, %Y %H:%M') }} UTC{% else %}No end time{% endif %}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Giveaway Modal -->
                            <div class="modal fade" id="giveawayModal{{ loop.index }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content" style="background: #36393f; color: #dcddde;">
                                        <div class="modal-header border-0">
                                            <h5 class="modal-title">Giveaway Details</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <h4 class="mb-3">{{ giveaway.item }}</h4>
                                            <p class="text-muted">Hosted by: <span class="text-white">{{ giveaway.host_user_id }}</span></p>
                                            
                                            <div class="mb-4">
                                                <h6>Requirements:</h6>
                                                <p class="text-white">{{ giveaway.requirements|replace('\n', '<br>')|safe }}</p>
                                            </div>
                                            
                                            <div class="row g-3 mb-3">
                                                <div class="col-md-6">
                                                    <div class="p-3 rounded" style="background: #2f3136;">
                                                        <div class="text-muted small">Winners</div>
                                                        <div class="h5 mb-0">{{ giveaway.winners }}</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="p-3 rounded" style="background: #2f3136;">
                                                        <div class="text-muted small">Entries</div>
                                                        <div class="h5 mb-0">{{ giveaway.entries|length }}</div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="p-3 rounded" style="background: #2f3136;">
                                                        <div class="text-muted small">Ends</div>
                                                        <div class="h6 mb-0">
                                                            {% if giveaway.end_time %}
                                                                {{ giveaway.end_time.strftime('%B %d, %Y at %H:%M') }} UTC
                                                            {% else %}
                                                                No end time
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-4">
                                                <h6>Participants ({{ giveaway.entries|length }})</h6>
                                                <div class="d-flex flex-wrap gap-2">
                                                    {% if giveaway.entries %}
                                                        {% for entry in giveaway.entries %}
                                                        <span class="badge bg-dark">{{ entry }}</span>
                                                        {% endfor %}
                                                    {% else %}
                                                        <span class="text-muted">No entries yet</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer border-0">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            {% set is_active = not giveaway.ended and (not giveaway.end_time or giveaway.end_time > now_utc) %}
                                            {% if is_active %}
                                            <a href="#" class="btn btn-primary">End Giveaway</a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No giveaways found</h5>
                            <p class="text-muted">Create your first giveaway to get started!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Giveaway Modal -->
<div class="modal fade" id="createGiveawayModal" tabindex="-1" aria-labelledby="createGiveawayModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: #36393f; color: #dcddde; border: 1px solid #202225;">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white">
                    <i class="fas fa-gift me-2"></i>Create Giveaway
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('create_giveaway') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_id" class="form-label">Channel</label>
                                <select class="form-select" id="channel_id" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                    {% for channel in channels %}
                                    <option value="{{ channel.id }}">#{{ channel.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Channel where the giveaway will be posted</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="host_user_id" class="form-label">Host User ID</label>
                                <input type="text" class="form-control" id="host_user_id" name="host_user_id" required pattern="\d{17,20}" inputmode="numeric" maxlength="20" placeholder="Enter Discord User ID (17-20 digits)">
                                <div class="form-text">Enter the Discord User ID of the host. Must be a member of this server.</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="item" class="form-label">Prize/Item</label>
                        <input type="text" class="form-control bg-dark border-dark text-white" id="item" name="item" required maxlength="100" placeholder="e.g., Discord Nitro, $50 Gift Card">
                        <div class="form-text">What is being given away (will be the embed title)</div>
                    </div>

                    <div class="mb-3">
                        <label for="requirements" class="form-label">Requirements</label>
                        <textarea class="form-control bg-dark border-dark text-white" id="requirements" name="requirements" rows="4" required maxlength="2000" placeholder="List the requirements for entering the giveaway (one per line)"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">One requirement per line</small>
                            <small class="text-muted"><span id="charCount">0</span>/2000 characters</small>
                        </div>
                        <div class="form-text">Entry requirements (will be the embed description)</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="winners" class="form-label">Number of Winners</label>
                                <input type="number" class="form-control" id="winners" name="winners" min="1" max="50" value="1" required>
                                <div class="form-text">How many winners to select (1-50)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Duration</label>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="duration_days" class="form-label">Days</label>
                                <input type="number" class="form-control" id="duration_days" name="duration_days" min="0" max="30" value="7">
                            </div>
                            <div class="col-md-4">
                                <label for="duration_hours" class="form-label">Hours</label>
                                <input type="number" class="form-control" id="duration_hours" name="duration_hours" min="0" max="23" value="0">
                            </div>
                            <div class="col-md-4">
                                <label for="duration_minutes" class="form-label">Minutes</label>
                                <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="1" max="59" value="0">
                            </div>
                        </div>
                        <div class="form-text">How long the giveaway should run (minimum 1 minute)</div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createGiveawayBtn">
                        <i class="fas fa-gift me-2"></i>Create Giveaway
                    </button>
                </div>
                <div class="px-3 pb-3">
                    <div class="progress" style="height: 3px; background-color: #2f3136;">
                        <div class="progress-bar" id="charLimitBar" role="progressbar" style="width: 0%; background-color: #5865f2;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1100;">
    <div id="toastContainer"></div>
</div>

<!-- Custom styles -->
<style>
    /* Toast styling */
    .toast-container {
        z-index: 1100;
    }
    .toast {
        margin-bottom: 1rem;
        opacity: 1;
        transition: opacity 0.3s ease-in-out;
    }
    .toast.show {
        opacity: 1;
    }
    
    /* Dark theme form controls */
    .form-control, .form-select {
        background-color: #2f3136 !important;
        border-color: #202225 !important;
        color: #dcddde !important;
    }
    .form-control:focus, .form-select:focus {
        background-color: #40444b !important;
        border-color: #5865f2 !important;
        box-shadow: 0 0 0 0.25rem rgba(88, 101, 242, 0.25) !important;
        color: #fff !important;
    }
    .form-control::placeholder {
        color: #72767d !important;
    }
</style>

<script>
// Form validation
document.getElementById('createGiveawayModal').addEventListener('show.bs.modal', function () {
    // Set default values when modal opens
    const form = this.querySelector('form');
    form.reset();
    document.getElementById('duration_days').value = '7';
    document.getElementById('duration_hours').value = '0';
    document.getElementById('duration_minutes').value = '0';
});

// Validate duration
function validateDuration() {
    const days = parseInt(document.getElementById('duration_days').value) || 0;
    const hours = parseInt(document.getElementById('duration_hours').value) || 0;
    const minutes = parseInt(document.getElementById('duration_minutes').value) || 0;
    
    // Calculate total minutes
    const totalMinutes = (days * 24 * 60) + (hours * 60) + minutes;
    const maxMinutes = 30 * 24 * 60; // 30 days in minutes
    
    if (totalMinutes > maxMinutes) {
        alert('Maximum giveaway duration is 30 days');
        return false;
    }
    
    if (days === 0 && hours === 0 && minutes === 0) {
        alert('Duration must be at least 1 minute');
        return false;
    }
    
    return true;
}

// Character counter for requirements textarea
const requirementsTextarea = document.getElementById('requirements');
const charCount = document.getElementById('charCount');
const charLimitBar = document.getElementById('charLimitBar');

if (requirementsTextarea) {
    requirementsTextarea.addEventListener('input', function() {
        const currentLength = this.value.length;
        const maxLength = parseInt(this.getAttribute('maxlength'));
        const percentage = (currentLength / maxLength) * 100;
        
        // Update counter text
        charCount.textContent = currentLength;
        
        // Update progress bar
        charLimitBar.style.width = `${percentage}%`;
        
        // Change color based on percentage
        if (percentage > 90) {
            charLimitBar.style.backgroundColor = '#ed4245';
        } else if (percentage > 75) {
            charLimitBar.style.backgroundColor = '#faa61a';
        } else {
            charLimitBar.style.backgroundColor = '#5865f2';
        }
    });
}

// Show toast notification
function showToast(type, message) {
    const toastContainer = document.getElementById('toastContainer');
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0 show`;
    toast.setAttribute('role','alert');
    toast.setAttribute('aria-live','assertive');
    toast.setAttribute('aria-atomic','true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Auto remove toast after 5 seconds
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// Add validation to form submit
document.querySelector('#createGiveawayModal form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate host user ID
    const hostUserId = document.getElementById('host_user_id');
    if (!hostUserId || !hostUserId.value || !/^\d{17,20}$/.test(hostUserId.value)) {
        showToast('danger', 'Please enter a valid Discord User ID (17-20 digits)');
        return false;
    }
    
    if (!validateDuration()) {
        return false;
    }
    
    // Disable submit button to prevent double submission
    const submitBtn = document.querySelector('#createGiveawayBtn');
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Creating...';
    
    // Get the form data
    const formData = new FormData(this);
    
    // Submit via fetch
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showToast('success', data.message || 'Giveaway created successfully!');
            
            // Close the modal
            const modalEl = document.getElementById('createGiveawayModal');
            const modal = bootstrap.Modal.getInstance(modalEl) || new bootstrap.Modal(modalEl);
            modal.hide();
            
            // Reset form
            this.reset();
            
            // Reset character counter
            if (charCount) {
                charCount.textContent = '0';
            }
            if (charLimitBar) {
                charLimitBar.style.width = '0%';
                charLimitBar.style.backgroundColor = '#5865f2';
            }
            
            // Reload the page after a short delay
            setTimeout(() => {
                window.location.href = data.redirect || window.location.href;
            }, 1500);
        } else {
            // Show error message
            showToast('danger', data.error || 'Failed to create giveaway. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('danger', 'An error occurred. Please try again.');
    })
    .finally(() => {
        // Re-enable the submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalBtnText;
    });
    
    return false;
});
</script>
{% endblock %}
