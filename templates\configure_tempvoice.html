﻿{% extends "base.html" %}

{% block title %}Configure Temp Voice - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link active" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-microphone text-info me-2"></i>Temp Voice Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Temporary Voice Channels</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="interface_channel_id" class="form-label">Interface Channel</label>
                                <select class="form-select" id="interface_channel_id" name="interface_channel_id" required>
                                    <option value="">Select a text channel...</option>
                                </select>
                                <div class="form-text">
                                    The text channel where the TempVoice interface will be posted
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="creator_channel_id" class="form-label">Creator Voice Channel</label>
                                <select class="form-select" id="creator_channel_id" name="creator_channel_id" required>
                                    <option value="">Select a voice channel...</option>
                                </select>
                                <div class="form-text">
                                    The voice channel users join to automatically create their temporary channel
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="default_user_limit" class="form-label">Default User Limit (Optional)</label>
                                <input type="number" class="form-control" id="default_user_limit" name="default_user_limit" 
                                       value="{{ tempvoice_settings.default_user_limit if tempvoice_settings and tempvoice_settings.default_user_limit else '' }}" 
                                       placeholder="e.g., 10" min="1" max="99">
                                <div class="form-text">
                                    Default user limit for new temporary channels (leave empty for no limit)
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if tempvoice_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Interface Channel:</strong>
                                <br><code>{{ tempvoice_settings.interface_channel_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Creator Channel:</strong>
                                <br><code>{{ tempvoice_settings.creator_channel_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Default User Limit:</strong>
                                <br><code>{{ tempvoice_settings.default_user_limit or 'No limit' }}</code>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get IDs</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on a channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The TempVoice system:</p>
                        <ul class="mb-0">
                            <li>Posts an interface in the text channel</li>
                            <li>Users join the creator voice channel</li>
                            <li>Bot automatically creates their temp channel</li>
                            <li>Users get full management controls</li>
                            <li>Channels auto-delete when empty</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>User Controls</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Channel owners can:</p>
                        <ul class="mb-0">
                            <li>Set user limits</li>
                            <li>Kick/block users</li>
                            <li>Lock/unlock channels</li>
                            <li>Transfer ownership</li>
                            <li>Rename channels</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Permissions</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Temporary channels:</p>
                        <ul class="mb-0">
                            <li>Copy @everyone permissions from creator channel</li>
                            <li>No special permissions for channel owner</li>
                            <li>All permissions come from the creator channel</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadChannels();
});

async function loadChannels() {
    try {
        const response = await fetch('{{ url_for("api_channels") }}');
        const data = await response.json();

        // Handle fallback cases (bot not connected)
        if (!response.ok || data.fallback) {
            throw new Error(data.message || data.error || 'Failed to load channels');
        }

        if (data.error && !data.fallback) {
            throw new Error(data.error);
        }

        loadTextChannels(data);
        loadVoiceChannels(data);
        console.log('Successfully loaded channels for tempvoice configuration');
    } catch (error) {
        console.error('Error loading channels:', error);
        showChannelLoadError(error.message);
    }
}

function showChannelLoadError(message) {
    // Show error for both selects
    const interfaceSelect = document.getElementById('interface_channel_id');
    const creatorSelect = document.getElementById('creator_channel_id');

    // Create error alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Bot Connection Issue:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert after the first form group
    const firstFormGroup = document.querySelector('.mb-3');
    firstFormGroup.parentNode.insertBefore(alertDiv, firstFormGroup.nextSibling);

    // Convert to text inputs with current values
    const currentInterfaceValue = '{{ tempvoice_settings.interface_channel_id if tempvoice_settings else "" }}';
    const currentCreatorValue = '{{ tempvoice_settings.creator_channel_id if tempvoice_settings else "" }}';

    interfaceSelect.outerHTML = `
        <input type="text" class="form-control" id="interface_channel_id" name="interface_channel_id"
               value="${currentInterfaceValue}"
               placeholder="Interface Channel ID (e.g., 123456789012345678)" required>
    `;

    creatorSelect.outerHTML = `
        <input type="text" class="form-control" id="creator_channel_id" name="creator_channel_id"
               value="${currentCreatorValue}"
               placeholder="Creator Channel ID (e.g., 123456789012345678)" required>
    `;
}

function loadTextChannels(channels) {
    const channelSelect = document.getElementById('interface_channel_id');
    if (!channelSelect) return;

    channelSelect.innerHTML = '<option value="">Select a text channel...</option>';

    // Filter to text channels only
    const textChannels = Array.isArray(channels) ? channels.filter(channel => {
        return channel.type === 'text' || channel.type === 0 || channel.type === '0';
    }) : [];

    if (textChannels.length === 0) {
        channelSelect.innerHTML = '<option value="">No text channels found</option>';
        return;
    }

    // Group by category
    const categorized = {};
    textChannels.forEach(channel => {
        const category = channel.category || 'No Category';
        if (!categorized[category]) {
            categorized[category] = [];
        }
        categorized[category].push(channel);
    });

    // Add channels grouped by category
    Object.keys(categorized).sort().forEach(category => {
        const optgroup = document.createElement('optgroup');
        optgroup.label = category;

        categorized[category].sort((a, b) => a.name.localeCompare(b.name)).forEach(channel => {
            const option = document.createElement('option');
            option.value = channel.id;
            option.textContent = `#${channel.name}`;

            // Select current channel if editing
            {% if tempvoice_settings and tempvoice_settings.interface_channel_id %}
            if (channel.id === '{{ tempvoice_settings.interface_channel_id }}') {
                option.selected = true;
            }
            {% endif %}

            optgroup.appendChild(option);
        });

        channelSelect.appendChild(optgroup);
    });
}

function loadVoiceChannels(channels) {
    const channelSelect = document.getElementById('creator_channel_id');
    channelSelect.innerHTML = '<option value="">Select a voice channel...</option>';

    // Filter to voice channels only
    const voiceChannels = Array.isArray(channels) ? channels.filter(channel => {
        return channel.type === 'voice' || channel.type === 2 || channel.type === '2';
    }) : [];

    if (voiceChannels.length === 0) {
        channelSelect.innerHTML = '<option value="">No voice channels found</option>';
        return;
    }

    // Group by category
    const categorized = {};
    voiceChannels.forEach(channel => {
        const category = channel.category || 'No Category';
        if (!categorized[category]) {
            categorized[category] = [];
        }
        categorized[category].push(channel);
    });

    // Add channels grouped by category
    Object.keys(categorized).sort().forEach(category => {
        if (category !== 'No Category') {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;
            channelSelect.appendChild(optgroup);

            categorized[category].forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.id;
                option.textContent = `ðŸ”Š ${channel.name}`;

                // Select current channel if editing
                {% if tempvoice_settings and tempvoice_settings.creator_channel_id %}
                if (channel.id === '{{ tempvoice_settings.creator_channel_id }}') {
                    option.selected = true;
                }
                {% endif %}

                optgroup.appendChild(option);
            });
        }
    });

    // Add uncategorized channels
    if (categorized['No Category']) {
        categorized['No Category'].forEach(channel => {
            const option = document.createElement('option');
            option.value = channel.id;
            option.textContent = `ðŸ”Š ${channel.name}`;

            // Select current channel if editing
            {% if tempvoice_settings and tempvoice_settings.creator_channel_id %}
            if (channel.id === '{{ tempvoice_settings.creator_channel_id }}') {
                option.selected = true;
            }
            {% endif %}

            channelSelect.appendChild(option);
        });
    }
}
</script>
{% endblock %}
