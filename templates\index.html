﻿{% extends "base.html" %}

{% block title %}Leakin Bot Dashboard{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <img src="{{ url_for('static', filename='logo.png') }}" alt="Leakin Bot" class="mb-4" style="max-width: 200px;">
                <h1 class="display-4 mb-3">
                    Leakin Bot Dashboard
                </h1>
                <p class="lead text-muted mb-4">
                    Manage your Discord server settings with ease
                </p>
                
                {% if 'user' not in session %}
                <div class="d-flex justify-content-center gap-3 mb-5">
                    <a href="{{ discord_auth_url }}" class="btn btn-lg btn-primary">
                        <i class="fab fa-discord me-2"></i> Login with Discord
                    </a>
                    <a href="https://leakin.cc" target="_blank" class="btn btn-lg btn-outline-secondary">
                        <i class="fas fa-shopping-cart me-2"></i> Purchase License
                    </a>
                </div>
                {% endif %}
            </div>

            {% if 'user' in session %}
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                Logged in as {{ user.username }}#{{ user.discriminator }}
                <a href="{{ url_for('logout') }}" class="float-end text-decoration-none">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
            {% endif %}

            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-star fa-3x text-warning mb-3"></i>
                            <h5>Repping System</h5>
                            <p class="text-muted">Automatic role assignment based on user status</p>
                            {% if 'user' in session %}
                            <a href="{{ url_for('configure_repping') }}" class="btn btn-sm btn-outline-warning mt-2">
                                Configure <i class="fas fa-cog ms-1"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-microphone fa-3x text-info mb-3"></i>
                            <h5>Temp Voice</h5>
                            <p class="text-muted">Temporary voice channels with full management</p>
                            {% if 'user' in session %}
                            <a href="{{ url_for('configure_tempvoice') }}" class="btn btn-sm btn-outline-info mt-2">
                                Configure <i class="fas fa-cog ms-1"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                            <h5>Anonymous Venting</h5>
                            <p class="text-muted">Safe space for anonymous messages</p>
                            {% if 'user' in session %}
                            <a href="{{ url_for('configure_vent') }}" class="btn btn-sm btn-outline-danger mt-2">
                                Configure <i class="fas fa-cog ms-1"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-thumbtack fa-3x text-success mb-3"></i>
                            <h5>Sticky Messages</h5>
                            <p class="text-muted">Persistent messages that auto-repost</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-ticket-alt fa-3x text-primary mb-3"></i>
                            <h5>DM Support</h5>
                            <p class="text-muted">DM-based support ticket system</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-3x text-secondary mb-3"></i>
                            <h5>And More!</h5>
                            <p class="text-muted">We have a lot of more features also!</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Access Dashboard
                </a>
                <div class="mt-3">
                    <small class="text-muted">
                        Need a license key? <a href="https://leakin.cc" target="_blank">Get one here</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
