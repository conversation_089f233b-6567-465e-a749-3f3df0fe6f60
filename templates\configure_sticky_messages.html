﻿{% extends "base.html" %}

{% block title %}Configure Sticky Messages - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link active" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-thumbtack text-success me-2"></i>Sticky Messages Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- Create Sticky Message -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Create Sticky Message</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="create">
                            <div class="mb-3">
                                <label for="channel_id" class="form-label">Channel</label>
                                <select class="form-select" id="channel_id" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                </select>
                                <div class="form-text">
                                    Channel where the sticky message will be posted
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">Message Content</label>
                                <textarea class="form-control" id="content" name="content" rows="4" 
                                          placeholder="Enter your sticky message content..." required></textarea>
                                <div class="form-text">
                                    The content of the sticky message (supports Discord markdown)
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Create Sticky Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Current Sticky Messages -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Current Sticky Messages</h6>
                        <span class="badge bg-info">{{ sticky_messages|length }} active</span>
                    </div>
                    <div class="card-body">
                        {% if sticky_messages %}
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>Channel</th>
                                        <th>Content Preview</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sticky in sticky_messages %}
                                    <tr>
                                        <td>
                                            <code>{{ sticky.channel_id }}</code>
                                            <br><small class="text-muted">Channel ID</small>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 300px;">
                                                {{ sticky.content[:100] }}{% if sticky.content|length > 100 %}...{% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="remove">
                                                <input type="hidden" name="channel_id" value="{{ sticky.channel_id }}">
                                                <button type="submit" class="btn btn-danger btn-sm" 
                                                        onclick="return confirm('Are you sure you want to remove this sticky message?')">
                                                    <i class="fas fa-trash me-1"></i>Remove
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No sticky messages are currently configured.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Sticky messages:</p>
                        <ul class="mb-0">
                            <li>Automatically repost when other messages are sent</li>
                            <li>Always stay at the bottom of the channel</li>
                            <li>Perfect for rules, announcements, or important info</li>
                            <li>Support Discord markdown formatting</li>
                            <li>One sticky message per channel</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Use **bold** and *italic* for emphasis</li>
                            <li>Add links with [text](url) format</li>
                            <li>Use emojis to make messages more engaging</li>
                            <li>Keep messages concise but informative</li>
                            <li>Test in a private channel first</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Features</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Automatic reposting</li>
                            <li>Rate limiting to prevent spam</li>
                            <li>Database persistence</li>
                            <li>Activity logging</li>
                            <li>Easy removal via dashboard</li>
                            <li>Premium feature with license validation</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12">
                                <h4 class="text-success">{{ sticky_messages|length }}</h4>
                                <small class="text-muted">Active Sticky Messages</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadChannels();
});

function loadChannels() {
    fetch('{{ url_for('api_channels') }}')
        .then(response => response.json())
        .then(channels => {
            const channelSelect = document.getElementById('channel_id');
            channelSelect.innerHTML = '<option value="">Select a channel...</option>';
            
            // Filter to text channels only
            const textChannels = channels.filter(channel => channel.type === 'text');
            
            // Group by category
            const categorized = {};
            textChannels.forEach(channel => {
                const category = channel.category || 'No Category';
                if (!categorized[category]) {
                    categorized[category] = [];
                }
                categorized[category].push(channel);
            });
            
            // Add channels grouped by category
            Object.keys(categorized).sort().forEach(category => {
                if (category !== 'No Category') {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = category;
                    channelSelect.appendChild(optgroup);
                    
                    categorized[category].forEach(channel => {
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = `# ${channel.name}`;
                        channelSelect.appendChild(option);
                    });
                }
            });
            
            // Add uncategorized channels
            if (categorized['No Category']) {
                categorized['No Category'].forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.id;
                    option.textContent = `# ${channel.name}`;
                    channelSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading channels:', error);
        });
}
</script>
{% endblock %}
