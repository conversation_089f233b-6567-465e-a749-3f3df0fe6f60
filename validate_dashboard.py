#!/usr/bin/env python3
"""
Dashboard validation script to check for common issues
"""

import os
import sys
import importlib.util
from pathlib import Path

def check_imports():
    """Check if all required modules can be imported"""
    print("🔍 Checking imports...")
    
    try:
        # Check if we can import the main modules
        import flask
        import pymongo
        import discord
        import jwt
        print("✅ Core dependencies available")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False
    
    try:
        # Check if local modules can be imported
        from database import DatabaseManager
        from oauth2 import get_oauth_url
        print("✅ Local modules can be imported")
    except ImportError as e:
        print(f"❌ Local module import error: {e}")
        return False
    
    return True

def check_env_variables():
    """Check if required environment variables are set"""
    print("\n🔍 Checking environment variables...")
    
    required_vars = [
        'MONGO_URL',
        'SECRET_KEY',
        'DISCORD_CLIENT_ID',
        'DISCORD_CLIENT_SECRET',
        'DISCORD_REDIRECT_URI'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def check_templates():
    """Check if template files exist and have basic structure"""
    print("\n🔍 Checking template files...")
    
    template_dir = Path("templates")
    if not template_dir.exists():
        print("❌ Templates directory not found")
        return False
    
    required_templates = [
        'base.html',
        'dashboard.html',
        'configure_repping.html',
        'configure_tempvoice.html',
        'settings.html',
        'logs.html',
        'stats.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        template_path = template_dir / template
        if not template_path.exists():
            missing_templates.append(template)
    
    if missing_templates:
        print(f"❌ Missing templates: {', '.join(missing_templates)}")
        return False
    else:
        print("✅ All required templates found")
        return True

def check_syntax():
    """Check Python syntax of main files"""
    print("\n🔍 Checking Python syntax...")
    
    files_to_check = [
        'web_dashboard.py',
        'database.py',
        'oauth2.py'
    ]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
            print(f"✅ {file_path} syntax OK")
        except SyntaxError as e:
            print(f"❌ Syntax error in {file_path}: {e}")
            return False
        except Exception as e:
            print(f"⚠️  Warning in {file_path}: {e}")
    
    return True

def check_api_routes():
    """Check if API routes are properly defined"""
    print("\n🔍 Checking API routes...")
    
    try:
        # Import the web dashboard to check routes
        spec = importlib.util.spec_from_file_location("web_dashboard", "web_dashboard.py")
        web_dashboard = importlib.util.module_from_spec(spec)
        
        # Check if the module can be loaded without errors
        print("✅ Web dashboard module can be loaded")
        return True
    except Exception as e:
        print(f"❌ Error loading web dashboard: {e}")
        return False

def main():
    """Run all validation checks"""
    print("🚀 Starting Discord Bot Dashboard Validation\n")
    
    checks = [
        check_imports,
        check_env_variables,
        check_templates,
        check_syntax,
        check_api_routes
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()  # Add spacing between checks
    
    print(f"📊 Validation Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All validation checks passed! Dashboard should be ready to run.")
        return True
    else:
        print("⚠️  Some validation checks failed. Please fix the issues before running the dashboard.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
