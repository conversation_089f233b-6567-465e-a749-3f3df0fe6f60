import os
import requests
import json
from urllib.parse import urlencode
from functools import wraps
from flask import redirect, url_for, session, request, jsonify
import jwt as pyjwt
from datetime import datetime, timedelta

# OAuth2 Configuration
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get configuration from environment variables with fallback to hardcoded values (for development)
DISCORD_CLIENT_ID = os.getenv('DISCORD_CLIENT_ID')
DISCORD_CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET')
DISCORD_REDIRECT_URI = os.getenv('DISCORD_REDIRECT_URI')
DISCORD_AUTH_URL = 'https://discord.com/api/oauth2/authorize'
DISCORD_TOKEN_URL = 'https://discord.com/api/oauth2/token'
DISCORD_API_BASE_URL = 'https://discord.com/api/v10'
SECRET_KEY = os.getenv('SECRET_KEY')

# Validate required configuration
if not DISCORD_CLIENT_ID or not DISCORD_CLIENT_SECRET:
    raise ValueError(
        "Missing required Discord OAuth2 configuration. "
        "Please set DISCORD_CLIENT_ID and DISCORD_CLIENT_SECRET environment variables."
    )

def get_oauth_url():
    """Generate the OAuth2 authorization URL for Discord"""
    params = {
        'client_id': DISCORD_CLIENT_ID,
        'redirect_uri': DISCORD_REDIRECT_URI,
        'response_type': 'code',
        'scope': 'identify email guilds'
    }
    return f"{DISCORD_AUTH_URL}?{urlencode(params)}"

def get_token(code):
    """Exchange authorization code for access token"""
    data = {
        'client_id': DISCORD_CLIENT_ID,
        'client_secret': DISCORD_CLIENT_SECRET,
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': DISCORD_REDIRECT_URI,
        'scope': 'identify email guilds'
    }
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    response = requests.post(DISCORD_TOKEN_URL, data=data, headers=headers)
    response.raise_for_status()
    return response.json()

def get_user_info(access_token):
    """Get user info using access token"""
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    
    response = requests.get(f"{DISCORD_API_BASE_URL}/users/@me", headers=headers)
    response.raise_for_status()
    return response.json()

def get_user_guilds(access_token):
    """Get user's guilds using access token"""
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    
    response = requests.get(f"{DISCORD_API_BASE_URL}/users/@me/guilds", headers=headers)
    response.raise_for_status()
    return response.json()

def create_jwt_token(user_data):
    """Create JWT token for session management"""
    payload = {
        'user_id': str(user_data['id']),
        'username': user_data['username'],
        'discriminator': user_data.get('discriminator', '0'),
        'email': user_data.get('email'),
        'avatar': user_data.get('avatar'),
        'exp': datetime.utcnow() + timedelta(days=1)
    }
    # Create the JWT token
    return pyjwt.encode(payload, SECRET_KEY, algorithm='HS256')

def login_required(f):
    """Decorator to require login for protected routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Allow requests to the select-server endpoint to handle their own authentication
        if request.endpoint == 'select_server_id':
            return f(*args, **kwargs)
            
        if 'token' not in session:
            # If this is an API request, return JSON error
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Session expired. Please log in again.', 'logout': True}), 401
            return redirect(url_for('login'))
            
        try:
            # Verify token
            payload = pyjwt.decode(session['token'], SECRET_KEY, algorithms=['HS256'])
            # Add user info to g object for easy access in routes
            from flask import g
            g.user = payload
            
            # Ensure user_id is in session for dashboard routes
            if 'user_id' not in session and request.endpoint in ['dashboard', 'select_server']:
                session['user_id'] = str(payload['user_id'])
                
        except pyjwt.ExpiredSignatureError:
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Session expired. Please log in again.', 'logout': True}), 401
            session.clear()
            flash('Your session has expired. Please log in again.', 'warning')
            return redirect(url_for('login'))
            
        except (pyjwt.InvalidTokenError, Exception) as e:
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Invalid session. Please log in again.', 'logout': True}), 401
            session.clear()
            flash('An error occurred during authentication. Please log in again.', 'danger')
            return redirect(url_for('login'))
            
        return f(*args, **kwargs)
    
    # Ensure the decorated function has the same name as the original
    decorated_function.__name__ = f.__name__
    return decorated_function

def get_license_keys(user_id):
    """Get all license keys associated with a Discord user"""
    # This should be implemented to fetch from your database
    # Placeholder implementation - replace with actual database query
    # Expects a db object to be passed in from the caller
    raise NotImplementedError("get_license_keys now requires a db parameter. Update your call to pass the db object.")
