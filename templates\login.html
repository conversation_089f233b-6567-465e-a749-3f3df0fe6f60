﻿{% extends "base.html" %}

{% block title %}Login - Leakin Bot Dashboard{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login to Dashboard</h4>
                </div>
                <div class="card-body p-5">
                    <div class="text-center mb-5">
                        <h2>Welcome Back!</h2>
                        <p class="lead text-muted">Sign in to access your Leakin Bot dashboard</p>
                    </div>
                    
                    <div class="row g-4">
                        <!-- Discord OAuth2 Login -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-4">
                                        <i class="fab fa-discord fa-4x text-discord mb-3"></i>
                                        <h4>Discord Login</h4>
                                        <p class="text-muted">Quick and secure login with your Discord account</p>
                                    </div>
                                    <a href="{{ url_for('login') }}" class="btn btn-discord w-100">
                                        <i class="fab fa-discord me-2"></i> Continue with Discord
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- License Key Login -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="mb-3">Or use License Key</h5>
                                    <form method="POST" action="{{ url_for('login_license') }}">
                                        <div class="mb-3">
                                            <label for="license_key" class="form-label">License Key</label>
                                            <input type="text" class="form-control" id="license_key" name="license_key" 
                                                   placeholder="XXXX-XXXX-XXXX-XXXX" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-key me-2"></i>Continue with License Key
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-5">
                        <p class="text-muted">
                            Don't have an account? 
                            <a href="https://leakin.cc" target="_blank" class="text-decoration-none">Get a license</a>
                        </p>
                        <p class="small text-muted">
                            By logging in, you agree to our 
                            <a href="#" class="text-decoration-none">Terms of Service</a> and 
                            <a href="#" class="text-decoration-none">Privacy Policy</a>.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Getting Started Section -->
            <div class="card mt-4 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-rocket me-2"></i>Getting Started</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <span class="fw-bold">1</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">Purchase a License</h6>
                                    <p class="small text-muted mb-0">Get your license key from our store</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <span class="fw-bold">2</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">Add the Bot</h6>
                                    <p class="small text-muted mb-0">Invite Leakin Bot to your server</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <span class="fw-bold">3</span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">Redeem Your Key</h6>
                                    <p class="small text-muted mb-0">Use <code>/redeem-key</code> in your server</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .btn-discord {
        background-color: #5865F2;
        color: white;
        transition: all 0.2s;
    }
    .btn-discord:hover {
        background-color: #4752c4;
        color: white;
        transform: translateY(-1px);
    }
    .text-discord {
        color: #5865F2;
    }
    .card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }
    .card-header {
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    .form-control:focus {
        border-color: #5865F2;
        box-shadow: 0 0 0 0.25rem rgba(88, 101, 242, 0.25);
    }
</style>

<style>
    /* Ensure the login form has proper spacing on mobile */
    @media (max-width: 576px) {
        .min-vh-100 {
            padding: 1rem;
        }
    }
    
    /* Make sure the login form is centered properly */
    .card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
        background-color: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color);
    }
    
    .card-footer {
        background-color: var(--bg-secondary);
        border-top: 1px solid var(--border-color);
    }
    
    .form-control:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
    }
</style>
{% endblock %}
