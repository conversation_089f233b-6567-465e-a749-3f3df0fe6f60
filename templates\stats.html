﻿{% extends "base.html" %}

{% block title %}Stats - {{ server_info.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* Progress bar styles */
.progress-thin {
    height: 4px;
}

/* Card styles */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* Icon shapes */
.icon-shape {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
}

/* Activity list */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

/* Progress bar width */
.progress-bar-custom {
    width: var(--progress-width, 0%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-1">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Server Statistics
                </h1>
                <p class="text-muted mb-0">Analytics and insights for your server</p>
            </div>
            <div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <!-- Total Members -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-primary rounded p-3">
                            <i class="fas fa-users text-primary"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Total Members</span>
                            <h3 class="mb-0">{{ server_info.member_count|default('0') }}</h3>
                        </div>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Online Members -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-success rounded p-3">
                            <i class="fas fa-signal text-success"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Online Now</span>
                            <h3 class="mb-0">{{ server_info.online_count|default('0') }}</h3>
                        </div>
                    </div>
                    {% set progress_width = (server_info.online_count / server_info.member_count * 100 if server_info.member_count and server_info.member_count > 0 else 0)|round|int %}
                    <div class="progress progress-thin">
                        <div class="progress-bar bg-success progress-bar-custom" 
                             role="progressbar" 
                             style="--progress-width: {{ progress_width }}%" 
                             aria-valuenow="{{ progress_width }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                            <span class="visually-hidden">{{ progress_width }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Channels -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-info rounded p-3">
                            <i class="fas fa-hashtag text-info"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Total Channels</span>
                            <h3 class="mb-0">{{ server_info.channel_count|default('0') }}</h3>
                        </div>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Server Boost -->
        <div class="col-md-6 col-xl-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="icon-shape bg-soft-warning rounded p-3">
                            <i class="fas fa-gem text-warning"></i>
                        </div>
                        <div class="text-end">
                            <span class="text-muted small">Server Boosts</span>
                            <h3 class="mb-0">{{ server_info.premium_subscription_count|default('0') }}</h3>
                        </div>
                    </div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity and Repping Section -->
    <div class="row g-4 mb-4">
        <!-- Activity Overview -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line text-primary me-2"></i>Activity Overview</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            Last 7 Days
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="timeRangeDropdown">
                            <li><a class="dropdown-item active" href="#">Last 7 Days</a></li>
                            <li><a class="dropdown-item" href="#">Last 30 Days</a></li>
                            <li><a class="dropdown-item" href="#">This Month</a></li>
                            <li><a class="dropdown-item" href="#">All Time</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Current Repping Users -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-star text-warning me-2"></i>Top Repping Users</h5>
                    <span class="badge bg-warning-subtle text-warning">{{ repping_users|length }} Active</span>
                </div>
                <div class="card-body p-0">
                    {% if repping_users %}
                        <div class="list-group list-group-flush">
                            {% for user in repping_users %}
                                <div class="list-group-item list-group-item-action d-flex align-items-center">
                                    <img src="{{ user.avatar_url }}" 
                                         class="rounded-circle me-3" 
                                         width="40" 
                                         height="40" 
                                         onerror="this.src='https://cdn.discordapp.com/embed/avatars/{{ range(0, 5)|random }}.png';"
                                         alt="{{ user.name }}">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ user.name }}</h6>
                                        <small class="text-muted">{{ user.rep_count }} reps</small>
                                    </div>
                                    <span class="badge bg-light text-dark">{{ user.last_rep|time_ago }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted p-4">
                            <div class="mb-3">
                                <i class="fas fa-star fa-3x text-warning opacity-25"></i>
                            </div>
                            <h6 class="mb-1">No Active Reps</h6>
                            <p class="small mb-0">Users will appear here when they start repping</p>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-transparent">
                    <a href="#" class="btn btn-sm btn-outline-primary w-100">View All Reps</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="row g-4">
        <!-- Recent Repping Activity -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history text-primary me-2"></i>Recent Repping Activity</h5>
                    <span class="badge bg-primary-subtle text-primary">{{ recent_activity|length }} Events</span>
                </div>
                <div class="card-body p-0">
                    {% if recent_activity %}
                        <div class="list-group list-group-flush" style="max-height: 400px; overflow-y: auto;">
                            {% for activity in recent_activity %}
                                <div class="list-group-item list-group-item-action">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <img src="{{ activity.user.avatar_url }}" 
                                                 class="rounded-circle" 
                                                 width="40" 
                                                 height="40"
                                                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/{{ range(0, 5)|random }}.png';"
                                                 alt="{{ activity.user.name }}">
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">{{ activity.user.name }}</h6>
                                                <small class="text-muted">{{ activity.timestamp|time_ago }}</small>
                                            </div>
                                            <p class="mb-0 text-muted small">
                                                {% if activity.type == 'rep_added' %}
                                                    <i class="fas fa-plus-circle text-success me-1"></i>Added rep
                                                {% else %}
                                                    <i class="fas fa-minus-circle text-danger me-1"></i>Removed rep
                                                {% endif %}
                                                {% if activity.reason %}
                                                    <span class="d-block text-truncate mt-1">{{ activity.reason }}</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted p-4">
                            <div class="mb-3">
                                <i class="fas fa-inbox fa-3x text-muted opacity-25"></i>
                            </div>
                            <h6 class="mb-1">No Recent Activity</h6>
                            <p class="small mb-0">Recent repping activity will appear here</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Server Insights -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie text-info me-2"></i>Server Insights</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <div class="icon-shape bg-primary bg-opacity-10 text-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-bolt fa-lg"></i>
                                    </div>
                                    <h3 class="mb-0">{{ server_stats.total_actions|default('0') }}</h3>
                                    <p class="text-muted small mb-0">Total Actions</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <div class="icon-shape bg-success bg-opacity-10 text-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-users fa-lg"></i>
                                    </div>
                                    <h3 class="mb-0">{{ server_stats.active_users|default('0') }}</h3>
                                    <p class="text-muted small mb-0">Active Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card bg-light h-100">
                                <div class="card-body">
                                    <h6 class="text-uppercase text-muted small fw-bold mb-3">Activity Distribution</h6>
                                    <canvas id="activityDistributionChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Activity Chart
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'line',
    data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: 'Messages',
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.3,
            fill: true
        }, {
            label: 'Voice Minutes',
            data: [28, 48, 40, 19, 86, 27, 90],
            borderColor: 'rgba(153, 102, 255, 1)',
            backgroundColor: 'rgba(153, 102, 255, 0.1)',
            tension: 0.3,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                mode: 'index',
                intersect: false,
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Activity Distribution Chart
const distCtx = document.getElementById('activityDistributionChart').getContext('2d');
const distChart = new Chart(distCtx, {
    type: 'doughnut',
    data: {
        labels: ['Repping', 'Voice', 'Messages', 'Moderation'],
        datasets: [{
            data: [30, 25, 25, 20],
            backgroundColor: [
                'rgba(255, 193, 7, 0.8)',
                'rgba(32, 201, 151, 0.8)',
                'rgba(13, 110, 253, 0.8)',
                'rgba(220, 53, 69, 0.8)'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '70%',
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Update time every second
function updateTime() {
    const now = new Date();
    document.getElementById('current-time').textContent = now.toLocaleString();
}

// Initial time update
updateTime();
setInterval(updateTime, 1000);
</script>
{% endblock %}

                        <h6 class="mb-0"><i class="fas fa-microphone me-2"></i>Active Temp Voice Channels ({{ active_temp_channels|length }})</h6>
                    </div>
                    <div class="card-body">
                        {% if active_temp_channels %}
                            <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                                {% for channel in active_temp_channels %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0 py-2">
                                    <span class="text-truncate">
                                        <i class="fas fa-volume-up me-2 text-muted"></i>{{ channel.owner_username }}
                                        {% if channel.user_limit %}
                                            <small class="text-muted">({{ channel.user_limit }} limit)</small>
                                        {% endif %}
                                        {% if channel.locked %}
                                            <i class="fas fa-lock text-warning ms-1" title="Locked"></i>
                                        {% endif %}
                                    </span>
                                    <small class="text-muted">{{ channel.created_at.strftime('%m/%d %H:%M') if channel.created_at else 'Unknown' }}</small>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-microphone-slash fa-2x mb-2"></i>
                                <p class="mb-0">No active temp voice channels</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Feature Statistics -->
        <div class="row">
            <!-- Repping Summary -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning">
                        <h6 class="mb-0"><i class="fas fa-star me-2"></i>Repping Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-warning">{{ server_stats.repping_users }}</h3>
                            <p class="text-muted">Users Currently Repping</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Temp Voice Summary -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info">
                        <h6 class="mb-0"><i class="fas fa-microphone me-2"></i>Temp Voice Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-info">{{ server_stats.active_temp_channels }}</h4>
                                <small class="text-muted">Currently Active</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info">{{ server_stats.total_temp_channels_30d }}</h4>
                                <small class="text-muted">Created (30 days)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sticky Messages -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success">
                        <h6 class="mb-0"><i class="fas fa-thumbtack me-2"></i>Sticky Messages</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-success">{{ server_stats.active_sticky_messages }}</h3>
                            <p class="text-muted">Active Sticky Messages</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gender Verification -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Gender Verification</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-primary">{{ server_stats.gender_tickets_30d }}</h3>
                            <p class="text-muted">Verification Tickets (30 days)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DM Support -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-secondary">
                        <h6 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>DM Support</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="text-light">{{ server_stats.dm_support_tickets_30d }}</h3>
                            <p class="text-muted">Support Tickets (30 days)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Update time every second
    function updateTime() {
        const now = new Date();
        document.getElementById('current-time').textContent = now.toLocaleString();
    }

    // Initial time update
    updateTime();
    setInterval(updateTime, 1000);
</script>
{% endblock %}
