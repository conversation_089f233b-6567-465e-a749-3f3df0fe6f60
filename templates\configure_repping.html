{% extends "base.html" %}

{% block title %}Configure Repping System - {{ server_name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link active" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-star text-warning me-2"></i>Repping System Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Automatic Role Assignment</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="trigger_word" class="form-label">Trigger Word</label>
                                <input type="text" class="form-control" id="trigger_word" name="trigger_word" 
                                       value="{{ config.trigger_word if config else '' }}" 
                                       placeholder="e.g., /leakin" required>
                                <div class="form-text">
                                    The word to look for in user custom statuses (case insensitive)
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="role_id" class="form-label">Role</label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <option value="">Select a role...</option>
                                </select>
                                <div class="form-text">
                                    The Discord role to assign to users with the trigger word in their status
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="channel_id" class="form-label">Notification Channel</label>
                                <select class="form-select" id="channel_id" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                </select>
                                <div class="form-text">
                                    The Discord channel where role assignment notifications will be sent
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if config and config.trigger_word %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Trigger Word:</strong>
                                <br><code>{{ config.trigger_word }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Role ID:</strong>
                                <br><code>{{ config.role_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Channel ID:</strong>
                                <br><code>{{ config.channel_id }}</code>
                            </div>
                        </div>
                        {% if config.log_channel_id %}
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ config.log_channel_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Ignored Users:</strong>
                                <br>{{ config.ignored_users|length if config.ignored_users else 0 }} users
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get IDs</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on a role or channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The repping system automatically:</p>
                        <ul class="mb-0">
                            <li>Monitors user custom statuses</li>
                            <li>Assigns roles when trigger word is found</li>
                            <li>Removes roles when trigger word is removed</li>
                            <li>Sends notifications to the configured channel</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Additional Settings</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">Use Discord commands for:</p>
                        <ul class="mb-0">
                            <li><code>/set-log-id</code> - Set detailed logging</li>
                            <li><code>/add-ignored-user</code> - Ignore specific users</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Global error handler
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to the top of the content
    const content = document.querySelector('.col-md-9');
    if (content) {
        content.insertBefore(alertDiv, content.firstChild);
    }
    
    console.error(message);
}

// Show loading state
function setLoading(element, isLoading) {
    const button = element.closest('form')?.querySelector('button[type="submit"]');
    if (button) {
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
        } else {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-save me-2"></i>Save Configuration';
        }
    }
}

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
    loadChannels();
    
    // Handle form submission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
});

async function loadRoles() {
    const roleSelect = document.getElementById('role_id');
    if (!roleSelect) return;

    roleSelect.innerHTML = '<option value="">Loading roles...</option>';

    try {
        const response = await fetch("{{ url_for('api_roles') }}");
        const data = await response.json();

        // Handle fallback cases (bot not connected)
        if (!response.ok || data.fallback) {
            throw new Error(data.message || data.error || 'Failed to load roles');
        }

        if (data.error && !data.fallback) {
            throw new Error(data.error);
        }

        roleSelect.innerHTML = '<option value="">Select a role...</option>';

        data.forEach(role => {
            const option = document.createElement('option');
            option.value = role.id;
            option.textContent = role.name;
            option.style.color = role.color !== '#000000' ? role.color : '';

            // Select current role if editing
            {% if config and config.role_id %}
            if (role.id === '{{ config.role_id }}') {
                option.selected = true;
            }
            {% endif %}

            roleSelect.appendChild(option);
        });

        console.log(`Successfully loaded ${data.length} roles`);
    } catch (error) {
        console.error('Error loading roles:', error);

        // Show user-friendly error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Bot Connection Issue:</strong> ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        roleSelect.parentNode.insertBefore(alertDiv, roleSelect.nextSibling);

        // Fallback to text input with current value
        const currentValue = '{{ config.role_id if config else "" }}';
        roleSelect.outerHTML = `
            <input type="text" class="form-control" id="role_id" name="role_id"
                   value="${currentValue}"
                   placeholder="Role ID (e.g., 123456789012345678)" required>
        `;

        // Add help text
        const helpDiv = document.createElement('div');
        helpDiv.className = 'form-text';
        helpDiv.innerHTML = '<i class="fas fa-info-circle me-1"></i>Enter the Discord role ID manually. Right-click a role → Copy ID (Developer Mode required)';
        document.getElementById('role_id').parentNode.appendChild(helpDiv);
    }
}

async function loadChannels() {
    const channelSelect = document.getElementById('channel_id');
    if (!channelSelect) return;

    channelSelect.innerHTML = '<option value="">Loading channels...</option>';

    try {
        const response = await fetch('{{ url_for("api_channels") }}', {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        });

        const data = await response.json();

        // Handle fallback cases (bot not connected)
        if (!response.ok || data.fallback) {
            throw new Error(data.message || data.error || 'Failed to load channels');
        }

        if (data.error && !data.fallback) {
            throw new Error(data.error);
        }

        channelSelect.innerHTML = '<option value="">Select a channel...</option>';

        // Filter to text channels only
        const textChannels = Array.isArray(data) ? data.filter(channel => {
            return channel.type === 'text' || channel.type === 0 || channel.type === '0';
        }) : [];

        if (textChannels.length === 0) {
            channelSelect.innerHTML = '<option value="">No text channels found</option>';
            return;
        }

        // Group by category
        const categorized = {};
        textChannels.forEach(channel => {
            const category = channel.category || 'No Category';
            if (!categorized[category]) {
                categorized[category] = [];
            }
            categorized[category].push(channel);
        });

        // Add channels grouped by category
        Object.keys(categorized).sort().forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;

            categorized[category].sort((a, b) => a.name.localeCompare(b.name)).forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.id;
                option.textContent = `#${channel.name}`;

                // Select current channel if editing
                const currentChannelId = '{{ config.channel_id if config else "" }}';
                if (currentChannelId && channel.id === currentChannelId) {
                    option.selected = true;
                }

                optgroup.appendChild(option);
            });

            channelSelect.appendChild(optgroup);
        });

        console.log(`Successfully loaded ${textChannels.length} text channels`);
    } catch (error) {
        console.error('Error loading channels:', error);

        // Show user-friendly error message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Bot Connection Issue:</strong> ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        channelSelect.parentNode.insertBefore(alertDiv, channelSelect.nextSibling);

        // Fallback to text input with current value
        const currentValue = '{{ config.channel_id if config else "" }}';
        channelSelect.outerHTML = `
            <input type="text" class="form-control" id="channel_id" name="channel_id"
                   value="${currentValue}"
                   placeholder="Channel ID (e.g., 123456789012345678)" required>
        `;

        // Add help text
        const helpDiv = document.createElement('div');
        helpDiv.className = 'form-text';
        helpDiv.innerHTML = '<i class="fas fa-info-circle me-1"></i>Enter the Discord channel ID manually. Right-click a channel → Copy ID (Developer Mode required)';
        document.getElementById('channel_id').parentNode.appendChild(helpDiv);
    }
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    try {
        setLoading(submitButton, true);
        
        const response = await fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const result = await response.json().catch(() => ({}));
        
        if (!response.ok) {
            throw new Error(result.error || 'Failed to save configuration');
        }
        
        // Show success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            Configuration saved successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        form.insertBefore(alertDiv, form.firstChild);
        
        // Scroll to top to show the message
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
    } catch (error) {
        console.error('Error saving configuration:', error);
        showError(error.message || 'An error occurred while saving the configuration.');
    } finally {
        setLoading(submitButton, false);
    }
}
</script>
{% endblock %}
