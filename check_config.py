import os
import sys
import urllib.parse
from dotenv import load_dotenv
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ConfigurationError, OperationFailure

def print_red(text):
    print(f"\033[91m{text}\033[0m")

def print_green(text):
    print(f"\033[92m{text}\033[0m")

def print_yellow(text):
    print(f"\033[93m{text}\033[0m")

# Load environment variables
print("Loading environment variables...")
env_file = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_file):
    print(f"Found .env file at: {env_file}")
    load_dotenv(env_file)
else:
    print_yellow("Warning: .env file not found, using system environment variables")
    load_dotenv()

# Get MongoDB URL from environment variables
print("\nChecking MongoDB configuration...")
MONGO_URL = os.getenv('MONGO_URL')
if not MONGO_URL:
    print_red("Error: MONGO_URL not found in environment variables")
    print("Make sure you have a .env file with MONGO_URL set")
    sys.exit(1)

# Print the connection string with password masked for security
masked_url = MONGO_URL.split('@', 1)
if len(masked_url) > 1:
    print(f"Using MongoDB URL: mongodb+srv://*****@{masked_url[1]}")
else:
    print(f"Using MongoDB URL: {MONGO_URL}")

# Connect to MongoDB
client = None
try:
    print("\nAttempting to connect to MongoDB...")
    
    # Create the client with a short timeout for faster failure
    client = MongoClient(
        MONGO_URL,
        serverSelectionTimeoutMS=5000,  # 5 second timeout
        connectTimeoutMS=10000,         # 10 second connection timeout
        socketTimeoutMS=30000,          # 30 second socket timeout
        retryWrites=True,
        w='majority'
    )
    
    # Test the connection
    print("Connection successful! Testing database access...")
    
    # Get database and collection
    db = client.get_database('leakin')
    print(f"Using database: {db.name}")
    
    # List all collections
    collections = db.list_collection_names()
    print(f"\nAvailable collections: {', '.join(collections) if collections else 'None'}")
    
    # Check for server configs collection
    if 'leakin-server-configs' not in collections:
        print_yellow("Warning: 'leakin-server-configs' collection not found")
        sys.exit(0)
    
    server_configs = db['leakin-server-configs']
    
    # Count documents
    count = server_configs.count_documents({})
    print(f"\nFound {count} server configuration(s)")
    
    if count == 0:
        print_yellow("No server configurations found in the database")
        sys.exit(0)
    
    # Get all server configs
    print("\nServer Configurations:")
    print("=" * 80)
    
    for config in server_configs.find():
        server_id = config.get('server_id', 'N/A')
        server_name = config.get('server_name', 'N/A')
        
        print(f"\nServer ID: {server_id}")
        print(f"Server Name: {server_name}")
        print("-" * 40)
        
        # Check required fields
        required_fields = {
            'repping_role_id': config.get('repping_role_id'),
            'repping_channel_id': config.get('repping_channel_id'),
            'trigger_word': config.get('trigger_word')
        }
        
        # Print required fields
        print("Required Configuration:")
        all_required_set = True
        for field, value in required_fields.items():
            status = "✅" if value else "❌"
            if not value:
                all_required_set = False
            print(f"  {status} {field}: {value or 'Not set'}")
        
        # Print configuration status
        if all_required_set:
            print_green("\n✅ This server is fully configured!")
        else:
            print_red("\n❌ This server is missing required configuration")
        
        # Print other relevant fields
        other_fields = [
            'vent_channel_id',
            'tempvoice_interface_channel_id',
            'tempvoice_creator_channel_id',
            'vent_enabled',
            'tempvoice_enabled',
            'created_at',
            'updated_at'
        ]
        
        print("\nOther Settings:")
        for field in other_fields:
            value = config.get(field)
            if value is not None:
                print(f"  • {field}: {value}")
        
        print("\n" + "=" * 80)
    
    print("\nDatabase check completed successfully!")

except ConnectionFailure as e:
    print_red(f"\n❌ Failed to connect to MongoDB: {e}")
    print("Please check the following:")
    print("1. Your internet connection")
    print("2. The MongoDB server is running and accessible")
    print("3. Your MONGO_URL in the .env file is correct")
    print("4. Your IP is whitelisted in MongoDB Atlas (if using Atlas)")
    sys.exit(1)

except ConfigurationError as e:
    print_red(f"\n❌ Invalid MongoDB configuration: {e}")
    print("Please check your MONGO_URL in the .env file")
    sys.exit(1)

except OperationFailure as e:
    print_red(f"\n❌ MongoDB operation failed: {e}")
    print("This is usually due to authentication or permission issues")
    print("Please check your MongoDB username, password, and database permissions")
    sys.exit(1)

except Exception as e:
    print_red(f"\n❌ An unexpected error occurred: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

finally:
    if client:
        client.close()
        print("\nMongoDB connection closed.")
