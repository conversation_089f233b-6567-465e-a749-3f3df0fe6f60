﻿{% extends "base.html" %}

{% block content %}
<!-- Loading Spinner Overlay -->
<div id="loadingSpinner" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background-color: rgba(0, 0, 0, 0.5); z-index: 9999; display: none !important;">
    <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<style>
    .server-icon-wrapper {
        width: 80px;
        height: 80px;
        border: 2px solid #4a4a4a !important;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto;
        background-color: transparent !important;
        box-shadow: none !important;
    }
    .server-icon {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .server-icon-initials {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 2rem;
        background-color: #6c757d;
        color: white;
    }
    .disabled-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.5);
    }
</style>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <h2 class="mb-4">Select a Server</h2>
            <p class="lead mb-5">Choose a server to manage from the options below</p>
            
            {% if not servers %}
                <div class="alert alert-warning">
                    <h4>No Servers Found</h4>
                    <p>You don't have access to any servers with active licenses.</p>
                    <a href="{{ url_for('no_license') }}" class="btn btn-primary mt-3">Get Help</a>
                </div>
            {% else %}
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    {% for server in servers %}
                        <div class="col">
                            <div class="card h-100 server-card {% if server.disabled %}server-disabled{% endif %}" 
                                 {% if server.disabled %}data-bs-toggle="tooltip" title="License Disabled"{% endif %}>
                                <div class="card-body text-center">
                                    <div class="server-icon-container mb-3">
                                        <div class="server-icon-wrapper position-relative">
                                            {% if server.icon %}
                                                <img src="{{ server.icon }}" 
                                                     alt="{{ server.name }}" 
                                                     class="server-icon"
                                                     onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            {% endif %}
                                            <div class="server-icon-initials" {% if server.icon %}style="display: none;"{% endif %}>
                                                {{ server.name|first|upper }}
                                            </div>
                                            {% if server.disabled %}
                                                <div class="disabled-overlay">
                                                    <i class="fas fa-ban fa-2x text-danger"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <h5 class="card-title mt-2">{{ server.name }}</h5>
                                    <p class="text-muted mb-3">
                                        <i class="fas fa-users me-2"></i>
                                        <span class="badge bg-primary rounded-pill">
                                            {% if server.member_count > 0 %}
                                                {{ server.member_count|number_format }} member{% if server.member_count != 1 %}s{% endif %}
                                            {% else %}
                                                <i class="fas fa-sync-alt fa-spin me-1"></i> Loading...
                                            {% endif %}
                                        </span>
                                    </p>
                                    <div class="server-id small text-muted mb-2">
                                        ID: {{ server.id }}
                                    </div>
                                    <button class="btn {% if server.disabled %}btn-outline-secondary disabled{% else %}btn-primary{% endif %} manage-server"
                                            data-server-id="{{ server.id }}"
                                            {% if server.disabled %}disabled{% endif %}>
                                        {% if server.disabled %}
                                            <i class="fas fa-lock me-1"></i> Disabled
                                        {% else %}
                                            <i class="fas fa-tachometer-alt me-1"></i> Manage Server
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="mt-5">
                <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt me-1"></i> Logout
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
});

// Add server selection handler
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add click handlers to all manage server buttons
    document.querySelectorAll('.manage-server').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const serverId = this.getAttribute('data-server-id');
            console.log('Manage server button clicked for server ID:', serverId);
            selectServer(serverId, this);
        });
    });
    
    console.log('Server selection page initialized');
});

// Function to handle server selection
async function selectServer(serverId, button) {
    console.log('selectServer called with serverId:', serverId);
    const loadingSpinner = document.getElementById('loadingSpinner');
    console.log('Loading spinner element:', loadingSpinner);
    
    if (!loadingSpinner) {
        console.error('Loading spinner element not found!');
        return;
    }
    
    const originalContent = button.innerHTML;
    
    try {
        console.log('Starting server selection process...');
        
        // Show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
        
        // Force show the spinner with !important
        loadingSpinner.style.setProperty('display', 'flex', 'important');
        console.log('Loading spinner should now be visible');
        
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '{{ csrf_token() }}';
        console.log('CSRF Token:', csrfToken ? 'Found' : 'Not found');
        
        console.log('Making fetch request to /select-server/' + serverId);
        // Make the API call to select the server
        const response = await fetch(`/select-server/${serverId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            },
            credentials: 'same-origin'  // Include cookies for session
        });
        
        console.log('Response status:', response.status, response.statusText);
        
        // Check if the response is JSON
        let data;
        try {
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            data = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON response:', e);
            throw new Error('Invalid server response');
        }
        
        if (!response.ok) {
            const errorMsg = data?.error || `Server returned ${response.status} status`;
            throw new Error(errorMsg);
        }
        
        if (!data.success) {
            throw new Error(data.error || 'Server returned an error');
        }
        
        // Redirect to dashboard on success
        window.location.href = data.redirect || '/dashboard';
    } catch (error) {
        console.error('Error selecting server:', error);
        
        // Check if this is a session expiration error
        if (error.message.includes('Session expired') || error.message.includes('401')) {
            showAlert('Your session has expired. Redirecting to login...', 'warning');
            setTimeout(() => {
                window.location.href = '{{ url_for("logout") }}';
            }, 1500);
        } else {
            showAlert(`Error: ${error.message}`, 'danger');
        }
    } finally {
        console.log('In finally block - cleaning up');
        
        // Reset all manage server buttons
        const buttons = document.querySelectorAll('.manage-server');
        buttons.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-tachometer-alt me-1"></i> Manage Server';
        });
        
        // Always ensure the loading spinner is hidden with !important
        if (loadingSpinner) {
            console.log('Hiding loading spinner');
            loadingSpinner.style.setProperty('display', 'none', 'important');
        } else {
            console.error('Loading spinner element not found during cleanup!');
        }
    }
}

// Function to show alert messages
function showAlert(message, type = 'danger') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to the top of the page
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alertDiv);
        bsAlert.close();
    }, 5000);
}

// Format numbers with commas
document.addEventListener('DOMContentLoaded', function() {
    // Format all elements with number-format class
    document.querySelectorAll('.number-format').forEach(function(el) {
        el.textContent = new Intl.NumberFormat().format(el.textContent);
    });
});
</script>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
.server-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.server-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.server-card.server-disabled {
    opacity: 0.7;
    background-color: #f8f9fa;
}

.server-icon-container {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
    border-radius: 16px; /* or 0 for square, or keep 50% for circle */
    background: transparent; /* <--- FIXED: No white/gray background */
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.server-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.server-icon-initials {
    font-size: 2.5rem;
    font-weight: bold;
    color: #6c757d;
}

.disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    border-radius: 50%;
}

.btn {
    min-width: 150px;
}

.alert {
    border-radius: 10px;
}
</style>
{% endblock %}
