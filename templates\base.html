﻿<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Leakin Bot Dashboard{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <style>
        :root {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --bg-hover: #2d3748;
            --border-color: #2d3748;
            --text-primary: #f8fafc;
            --text-secondary: #94a3b8;
            --accent-color: #4f46e5;
            --accent-hover: #4338ca;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --sidebar-width: 280px;
            --header-height: 60px;
            --transition-speed: 0.2s;
            --border-radius: 10px;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Navigation */
        .navbar {
            height: var(--header-height);
            background-color: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color);
            padding: 0 1.5rem;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary) !important;
        }

        .nav-link {
            color: var(--text-secondary);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all var(--transition-speed) ease;
        }

        .nav-link:hover, .nav-link.active {
            background-color: var(--bg-hover);
            color: var(--text-primary);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 2rem;
            margin-top: var(--header-height);
        }

        /* Server Card */
        .server-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .server-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--box-shadow);
        }

        .server-card.disabled {
            opacity: 0.7;
            position: relative;
        }

        .server-card.disabled::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .server-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
        }

        .server-initials {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        /* Buttons */
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all var(--transition-speed) ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn i {
            font-size: 0.9em;
        }

        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-primary:hover {
            background-color: var(--accent-hover);
            border-color: var(--accent-hover);
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
        }

        /* Utilities */
        .text-muted {
            color: var(--text-secondary) !important;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <!-- Brand/logo -->
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-robot"></i>
                <span>Leakin</span>
            </a>
            
            <!-- Mobile menu button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navbar content -->
            <div class="collapse navbar-collapse" id="mainNavbar">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'dashboard' }}" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    
                    <!-- Server Selector -->
                    {% if 'user_servers' in session and session.user_servers %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'select_server' }}" 
                           href="{{ url_for('select_server') }}"
                           data-bs-toggle="tooltip" 
                           data-bs-placement="bottom" 
                           title="Switch servers">
                            {% set current_server = none %}
                            {% if 'server_id' in session %}
                                {% for server in session.user_servers %}
                                    {% if server.id == session.server_id %}
                                        {% set current_server = server %}
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                            
                            {% if current_server %}
                                {% if current_server.icon %}
                                <img src="https://cdn.discordapp.com/icons/{{ current_server.id }}/{{ current_server.icon }}.png?size=64" 
                                     alt="{{ current_server.name }}" 
                                     class="rounded-circle me-1" 
                                     style="width: 24px; height: 24px; object-fit: cover;">
                                {% else %}
                                <div class="d-inline-flex align-items-center justify-content-center bg-secondary rounded-circle me-1" 
                                     style="width: 24px; height: 24px;">
                                    <span class="small">{{ current_server.name[0]|upper }}</span>
                                </div>
                                {% endif %}
                                <span class="d-none d-md-inline">{{ current_server.name }}</span>
                            {% else %}
                                <i class="fas fa-server"></i>
                                <span class="d-none d-md-inline">Select Server</span>
                            {% endif %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- User Menu -->
                {% if 'discord_user' in session %}
                <div class="dropdown">
                    <a href="#" class="nav-link dropdown-toggle" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        {% if session.discord_user.avatar %}
                        <img src="https://cdn.discordapp.com/avatars/{{ session.discord_user.id }}/{{ session.discord_user.avatar }}.png?size=64" 
                             alt="User Avatar" 
                             class="rounded-circle me-1" 
                             style="width: 24px; height: 24px; object-fit: cover;">
                        {% else %}
                        <div class="d-inline-flex align-items-center justify-content-center bg-secondary rounded-circle me-1" 
                             style="width: 24px; height: 24px;">
                            <i class="fas fa-user"></i>
                        </div>
                        {% endif %}
                        <span class="d-none d-md-inline">{{ session.discord_user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('settings') }}"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
                {% else %}
                <a href="{{ url_for('login') }}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login with Discord</span>
                </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add CSRF token to all AJAX requests
        document.addEventListener('DOMContentLoaded', function() {
            // Get CSRF token from meta tag or create one
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            
            if (!csrfToken) {
                // If no CSRF token found, create a new one
                csrfToken = '{{ csrf_token() }}';
                const meta = document.createElement('meta');
                meta.name = 'csrf-token';
                meta.content = csrfToken;
                document.head.appendChild(meta);
            }
            
            // Set up AJAX to include CSRF token in all requests
            const csrfSafeMethod = (method) => {
                // These HTTP methods do not require CSRF protection
                return (/^(GET|HEAD|OPTIONS|TRACE)$/.test(method));
            };
            
            // Set up AJAX to include CSRF token in all requests
            const csrftoken = csrfToken;
            
            // Function to get cookie by name
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        // Does this cookie string begin with the name we want?
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
            
            // Set up AJAX to include CSRF token in all requests
            const csrftoken2 = getCookie('csrftoken') || csrfToken;
            
            // Set up AJAX to include CSRF token in all requests
            const xhrOpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                if (!csrfSafeMethod(method) && !this._setXHRHeaders) {
                    this._setXHRHeaders = true;
                    this.setRequestHeader('X-CSRFToken', csrftoken2);
                    this.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                }
                return xhrOpen.apply(this, [method, url, ...args]);
            };
            
            // Override fetch to include CSRF token
            const originalFetch = window.fetch;
            window.fetch = function(resource, options = {}) {
                // Set up default headers if they don't exist
                options.headers = options.headers || {};
                
                // Add CSRF token for non-GET requests
                if (options.method && !csrfSafeMethod(options.method)) {
                    options.headers = {
                        ...options.headers,
                        'X-CSRFToken': csrftoken2,
                        'X-Requested-With': 'XMLHttpRequest'
                    };
                    
                    // Ensure credentials are included for cross-origin requests
                    options.credentials = 'same-origin';
                }
                
                return originalFetch(resource, options);
            };
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
