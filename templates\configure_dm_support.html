﻿{% extends "base.html" %}

{% block title %}Configure DM Support - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="{{ url_for('configure_sticky_messages') }}">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link active" href="{{ url_for('configure_dm_support') }}">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="{{ url_for('configure_gender_verification') }}">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('settings') }}">
                    <i class="fas fa-cog"></i>Settings
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="fas fa-file-alt"></i>Logs
                </a>
                <a class="nav-link" href="{{ url_for('stats') }}">
                    <i class="fas fa-chart-bar"></i>Stats
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-ticket-alt text-primary me-2"></i>DM Support Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        {% if not config or not config.log_channel_id %}
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Log Channel Required</h6>
            <p class="mb-0">
                The DM support system requires a log channel to be configured first for moderation purposes. 
                Please configure the log channel in <a href="{{ url_for('settings') }}">Settings</a> first.
            </p>
        </div>
        {% endif %}

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure DM-Based Support System</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">Support Category</label>
                                <select class="form-select" id="category_id" name="category_id" required
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <option value="">Select a category...</option>
                                </select>
                                <div class="form-text">
                                    Category where support ticket channels will be created
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="support_role_id" class="form-label">Support Role</label>
                                <select class="form-select" id="support_role_id" name="support_role_id" required
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <option value="">Select a role...</option>
                                </select>
                                <div class="form-text">
                                    Role that can respond to and manage support tickets
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" 
                                        {% if not config or not config.log_channel_id %}disabled{% endif %}>
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if dm_support_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Category ID:</strong>
                                <br><code>{{ dm_support_settings.category_id }}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>Support Role ID:</strong>
                                <br><code>{{ dm_support_settings.support_role_id }}</code>
                            </div>
                        </div>
                        {% if config and config.log_channel_id %}
                        <hr>
                        <div class="row">
                            <div class="col-md-12">
                                <strong>Log Channel ID:</strong>
                                <br><code>{{ config.log_channel_id }}</code>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The DM support system:</p>
                        <ul class="mb-0">
                            <li>Users DM the bot to create tickets</li>
                            <li>Bot creates private channels in the category</li>
                            <li>Support team responds via the channel</li>
                            <li>Messages are forwarded to user DMs</li>
                            <li>One ticket per user globally</li>
                            <li>Automatic transcript logging</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Features</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Server selection for multi-server setups</li>
                            <li>Confirmation flow for ticket creation</li>
                            <li>Admin responses via embeds</li>
                            <li>Automatic cleanup when resolved</li>
                            <li>Complete transcript logging</li>
                            <li>Close tickets with <code>=close reason</code></li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Requirements</h6>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li>Log channel must be configured</li>
                            <li>Bot needs manage channels permission</li>
                            <li>Support role should have appropriate permissions</li>
                            <li>Category should have proper permissions</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    {% if config and config.log_channel_id %}
    loadCategories();
    loadRoles();
    {% endif %}
});

function loadCategories() {
    fetch('{{ url_for('api_channels') }}')
        .then(response => response.json())
        .then(channels => {
            const categorySelect = document.getElementById('category_id');
            categorySelect.innerHTML = '<option value="">Select a category...</option>';
            
            // Filter to category channels only
            const categories = channels.filter(channel => channel.type === 'category');
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                
                // Select current category if editing
                {% if dm_support_settings and dm_support_settings.category_id %}
                if (category.id === '{{ dm_support_settings.category_id }}') {
                    option.selected = true;
                }
                {% endif %}
                
                categorySelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading categories:', error);
        });
}

function loadRoles() {
    fetch('{{ url_for('api_roles') }}')
        .then(response => response.json())
        .then(roles => {
            const roleSelect = document.getElementById('support_role_id');
            roleSelect.innerHTML = '<option value="">Select a role...</option>';
            
            roles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.name;
                
                // Select current role if editing
                {% if dm_support_settings and dm_support_settings.support_role_id %}
                if (role.id === '{{ dm_support_settings.support_role_id }}') {
                    option.selected = true;
                }
                {% endif %}
                
                roleSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading roles:', error);
        });
}
</script>
{% endblock %}
