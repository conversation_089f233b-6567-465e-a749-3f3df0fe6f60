﻿{% extends "base.html" %}

{% block title %}Select Server - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container d-flex align-items-center justify-content-center" style="min-height: 80vh;">
    <div class="row w-100 justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="card shadow-lg border-0 rounded-3">
                <div class="card-body p-5">
                    <div class="text-center mb-5">
                        <h2 class="mb-3">Select a Server</h2>
                        <p class="text-muted">Choose a server to manage its settings and features.</p>
                    </div>
                    
                    <div class="row g-4">
                        {% for server in servers %}
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 border-0 shadow-sm hover-lift" id="server-{{ server.id }}">
                                <div class="card-body text-center p-4">
                                    <div class="d-flex justify-content-center mb-2">
                                        {% if server.icon %}
                                            <img src="https://cdn.discordapp.com/icons/{{ server.id }}/{{ server.icon }}.png?size=128" 
                                                 alt="{{ server.name }}" 
                                                 style="width: 64px; height: 64px; object-fit: cover; border-radius: 12px; background: none; box-shadow: none; margin: 0;">
                                        {% else %}
                                            <div class="d-flex align-items-center justify-content-center bg-secondary"
                                                 style="width: 64px; height: 64px; border-radius: 12px; margin: 0;">
                                                <span class="h4 mb-0 text-white">{{ server.name[0]|upper }}</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <h5 class="mb-1">{{ server.name }}</h5>
                                    <p class="text-muted small mb-0">{{ server.member_count }} members</p>
                                </div>
                                <div class="card-footer bg-transparent border-0 pt-0">
                                    <button class="btn btn-primary w-100 manage-server" data-server-id="{{ server.id }}">
                                        <i class="fas fa-cog me-2"></i>Manage Server
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="text-center mt-5 pt-4 border-top">
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Spinner Template -->
<div id="loadingSpinner" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background-color: rgba(0,0,0,0.5); z-index: 9999; display: none;">
    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<style>
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2) !important;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle server selection
    document.querySelectorAll('.manage-server').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const serverId = this.getAttribute('data-server-id');
            selectServer(serverId);
        });
    });

    // Function to handle server selection
    async function selectServer(serverId) {
        const button = document.querySelector(`.manage-server[data-server-id="${serverId}"]`);
        const loadingSpinner = document.getElementById('loadingSpinner');
        
        try {
            // Show loading state
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            loadingSpinner.style.display = 'flex';
            
            // Get CSRF token from meta tag or form
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '{{ csrf_token() }}';
            
            // Make the API call to select the server
            const response = await fetch(`/select-server/${serverId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken
                },
                credentials: 'same-origin'  // Include cookies for session
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                // Redirect to dashboard on success
                window.location.href = data.redirect || '/dashboard';
            } else {
                // Show error message
                const errorMsg = data.error || 'Failed to select server. Please try again.';
                showAlert(errorMsg, 'danger');
                
                // If the error indicates session expired, redirect to login
                if (data.logout) {
                    setTimeout(() => {
                        window.location.href = '{{ url_for("logout") }}';
                    }, 1500);
                    return;
                }
            }
        } catch (error) {
            console.error('Error selecting server:', error);
            showAlert('An error occurred while selecting the server. Please try again.', 'danger');
        } finally {
            // Reset button state
            const buttons = document.querySelectorAll('.manage-server');
            buttons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-cog me-2"></i>Manage Server';
            });
            loadingSpinner.style.display = 'none';
        }
    }
    
    // Function to show alert messages
    function showAlert(message, type = 'danger') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        // Add to the top of the page
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }, 5000);
    }
});
</script>
{% endblock %}
